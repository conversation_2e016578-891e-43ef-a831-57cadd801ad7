package com.zto.devops.qc.infrastructure.aspect.sign.auth;

import cn.hutool.json.JSONUtil;
import com.alibaba.dubbo.rpc.RpcContext;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.model.auth.ApiKeyInfo;
import com.zto.devops.qc.client.model.auth.SignAuthContext;
import com.zto.devops.qc.domain.annotation.QcApiSignAuth;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.infrastructure.config.QcApiSignAuthConfig;
import com.zto.devops.qc.infrastructure.util.SignatureUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Aspect
@Component
public class QcApiSignAuthAspect {

    @Autowired
    private RedisService redisService;
    @Autowired
    private QcApiSignAuthConfig authConfig;

    /**
     * 拦截带有@ApiSignAuth注解的方法
     */
    @Around("@annotation(com.zto.devops.qc.domain.annotation.QcApiSignAuth)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        QcApiSignAuth authAnnotation = method.getAnnotation(QcApiSignAuth.class);
        if (authAnnotation != null && (!authConfig.isEnabled() || !authAnnotation.enabled())) {
            log.info("API签名鉴权已禁用，跳过验证");
            return joinPoint.proceed();
        }

        try {
            validateSignature(authAnnotation, joinPoint);
            if (authConfig.isLogEnabled()) {
                log.info("API签名鉴权通过: method={}", method.getName());
            }
            return joinPoint.proceed();
        } catch (Exception e) {
            log.error("API签名鉴权失败: method={}, error={}", method.getName(), e.getMessage());
            throw e;
        }
    }

    /**
     * 验证签名
     */
    private void validateSignature(QcApiSignAuth authAnnotation, ProceedingJoinPoint joinPoint) {
        //获取签名上下文
        SignAuthContext context = extractFromRpcAttachment();
        if (context == null) {
            throw new ServiceException("缺少签名参数");
        }

        //验证必要参数
        validateRequiredParams(context);

        //验证时间戳
        validateTimestamp(context.getTimestamp(), authAnnotation);

        //验证nonce（防重放）
        validateNonce(context.getNonce(), authAnnotation);

        //获取密钥信息
        ApiKeyInfo apiKeyInfo = getApiKeyInfo(context.getAccessKey());
        if (apiKeyInfo == null) {
            throw new ServiceException("无效的AccessKey");
        }

        //验证签名
        validateSignatureValue(context, apiKeyInfo);
    }

    /**
     * 验证必要参数
     */
    private void validateRequiredParams(SignAuthContext context) {
        if (StringUtils.isBlank(context.getAccessKey())) {
            throw new ServiceException("缺少AccessKey");
        }
        if (StringUtils.isBlank(context.getSignature())) {
            throw new ServiceException("缺少Signature");
        }
        if (StringUtils.isBlank(context.getNonce())) {
            throw new ServiceException("缺少Nonce");
        }
        if (context.getTimestamp() <= 0) {
            throw new ServiceException("无效的Timestamp");
        }
    }

    /**
     * 验证时间戳
     */
    private void validateTimestamp(long timestamp, QcApiSignAuth authAnnotation) {
        long expireSeconds = authAnnotation.expireSeconds() > 0
                ? authAnnotation.expireSeconds()
                : authConfig.getDefaultExpireSeconds();

        long timeSkewSeconds = authAnnotation.timeSkewSeconds() > 0
                ? authAnnotation.timeSkewSeconds()
                : authConfig.getDefaultTimeSkewSeconds();

        if (!SignatureUtils.isTimestampValid(timestamp, expireSeconds, timeSkewSeconds)) {
            throw new ServiceException("签名已过期或时间戳无效");
        }
    }

    /**
     * 验证nonce（防重放攻击）
     */
    private void validateNonce(String nonce, QcApiSignAuth authAnnotation) {
        String nonceKey = String.format("%s:nonce:%s", authConfig.getRedisCachePrefix(), nonce);
        if (redisService.hasKey(nonceKey)) {
            throw new ServiceException("重复的请求（Nonce已使用）");
        }
        long expireSeconds = Math.max(authAnnotation.expireSeconds(), authConfig.getNonceCacheExpireSeconds());
        redisService.setKey(nonceKey, nonceKey, expireSeconds, TimeUnit.SECONDS);
    }

    /**
     * 验证签名值
     */
    private void validateSignatureValue(SignAuthContext context, ApiKeyInfo apiKeyInfo) {
        boolean isValid = SignatureUtils.verifySignature(
                apiKeyInfo.getSecretKey(),
                context.getSignature(),
                context.getRequestUri(),
                context.getTimestamp(),
                context.getNonce()
        );
        if (!isValid) {
            throw new ServiceException("签名验证失败");
        }
    }

    /**
     * 获取秘钥信息
     */
    private ApiKeyInfo getApiKeyInfo(String accessKey) {
        String keySecret = authConfig.getKeySecret();
        if (StringUtils.isBlank(keySecret)) {
            return null;
        }
        List<ApiKeyInfo> list = JSONUtil.toList(keySecret, ApiKeyInfo.class);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().filter(t -> t.getAccessKey().equals(accessKey)).findAny().orElse(null);
    }

    /**
     * 从RPC附件中获取签名上下文
     */
    private SignAuthContext extractFromRpcAttachment() {
        RpcContext rpcContext = RpcContext.getContext();
        if (rpcContext == null) {
            return null;
        }
        try {
            SignAuthContext context = new SignAuthContext();
            context.setRequestUri(rpcContext.getAttachment("gateway.apiName"));
            context.setAccessKey(rpcContext.getAttachment("x-qca-accessKey"));
            context.setTimestamp(Long.parseLong(rpcContext.getAttachment("x-qca-timestamp")));
            context.setNonce(rpcContext.getAttachment("x-qca-nonce"));
            context.setSignature(rpcContext.getAttachment("x-qca-signature"));
            return context;
        } catch (NumberFormatException e) {
            log.warn("时间戳转换异常");
        }
        return null;
    }
}
