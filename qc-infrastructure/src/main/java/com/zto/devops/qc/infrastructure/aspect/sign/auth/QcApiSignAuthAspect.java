package com.zto.devops.qc.infrastructure.aspect.sign.auth;

import cn.hutool.json.JSONUtil;
import com.alibaba.dubbo.rpc.RpcContext;
import com.zto.devops.framework.client.exception.ServiceException;
import com.zto.devops.framework.common.util.CollectionUtil;
import com.zto.devops.qc.client.model.auth.ApiKeyInfo;
import com.zto.devops.qc.client.model.auth.SignAuthContext;
import com.zto.devops.qc.domain.annotation.QcApiSignAuth;
import com.zto.devops.qc.domain.gateway.redis.RedisService;
import com.zto.devops.qc.infrastructure.config.QcApiSignAuthConfig;
import com.zto.devops.qc.infrastructure.util.AuthorizationHeaderParser;
import com.zto.devops.qc.infrastructure.util.SignatureUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Aspect
@Component
public class QcApiSignAuthAspect {

    @Autowired
    private RedisService redisService;
    @Autowired
    private QcApiSignAuthConfig authConfig;

    /**
     * 拦截带有@ApiSignAuth注解的方法
     */
    @Around("@annotation(com.zto.devops.qc.domain.annotation.QcApiSignAuth)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        QcApiSignAuth authAnnotation = method.getAnnotation(QcApiSignAuth.class);
        if (authAnnotation != null && (!authConfig.isEnabled() || !authAnnotation.enabled())) {
            log.info("API签名鉴权已禁用，跳过验证");
            return joinPoint.proceed();
        }

        try {
            validateSignature(authAnnotation, joinPoint);
            if (authConfig.isLogEnabled()) {
                log.info("API签名鉴权通过: method={}", method.getName());
            }
            return joinPoint.proceed();
        } catch (Exception e) {
            log.error("API签名鉴权失败: method={}, error={}", method.getName(), e.getMessage());
            throw e;
        }
    }

    /**
     * 验证签名
     */
    private void validateSignature(QcApiSignAuth authAnnotation, ProceedingJoinPoint joinPoint) {
        // 获取签名上下文
        SignAuthContext context = extractFromRpcAttachment();
        if (context == null) {
            throw new ServiceException("缺少签名参数");
        }

        // 验证必要参数
        validateRequiredParams(context);

        // 验证时间戳
        validateTimestamp(context.getTimestamp(), authAnnotation);

        // 验证nonce（防重放）
        validateNonce(context.getNonce(), authAnnotation);

        // 获取密钥信息
        ApiKeyInfo apiKeyInfo = getApiKeyInfo(context.getAccessKey());
        if (apiKeyInfo == null) {
            throw new ServiceException("无效的AccessKey");
        }

        // 根据认证格式验证签名
        if (context.isStandardFormat()) {
            validateStandardSignature(context, apiKeyInfo);
        } else {
            validateLegacySignature(context, apiKeyInfo);
        }
    }

    /**
     * 验证必要参数
     */
    private void validateRequiredParams(SignAuthContext context) {
        if (StringUtils.isBlank(context.getAccessKey())) {
            throw new ServiceException("缺少AccessKey");
        }
        if (StringUtils.isBlank(context.getSignature())) {
            throw new ServiceException("缺少Signature");
        }
        if (StringUtils.isBlank(context.getNonce())) {
            throw new ServiceException("缺少Nonce");
        }
        if (context.getTimestamp() <= 0) {
            throw new ServiceException("无效的Timestamp");
        }
    }

    /**
     * 验证时间戳
     */
    private void validateTimestamp(long timestamp, QcApiSignAuth authAnnotation) {
        long expireSeconds = authAnnotation.expireSeconds() > 0
                ? authAnnotation.expireSeconds()
                : authConfig.getDefaultExpireSeconds();

        long timeSkewSeconds = authAnnotation.timeSkewSeconds() > 0
                ? authAnnotation.timeSkewSeconds()
                : authConfig.getDefaultTimeSkewSeconds();

        if (!SignatureUtils.isTimestampValid(timestamp, expireSeconds, timeSkewSeconds)) {
            throw new ServiceException("签名已过期或时间戳无效");
        }
    }

    /**
     * 验证nonce（防重放攻击）
     */
    private void validateNonce(String nonce, QcApiSignAuth authAnnotation) {
        String nonceKey = String.format("%s:nonce:%s", authConfig.getRedisCachePrefix(), nonce);
        if (redisService.hasKey(nonceKey)) {
            throw new ServiceException("重复的请求（Nonce已使用）");
        }
        long expireSeconds = Math.max(authAnnotation.expireSeconds(), authConfig.getNonceCacheExpireSeconds());
        redisService.setKey(nonceKey, nonceKey, expireSeconds, TimeUnit.SECONDS);
    }

    /**
     * 验证标准格式签名
     */
    private void validateStandardSignature(SignAuthContext context, ApiKeyInfo apiKeyInfo) {
        // 验证SignedHeaders
        validateSignedHeaders(context);

        boolean isValid = SignatureUtils.verifyStandardSignature(
                apiKeyInfo.getSecretKey(),
                context.getSignature(),
                context.getHttpMethod(),
                context.getRequestUri(),
                context.getQueryParams(),
                context.getHeaders(),
                context.getSignedHeaders(),
                context.getPayload()
        );
        if (!isValid) {
            throw new ServiceException("标准签名验证失败");
        }
    }

    /**
     * 验证兼容格式签名
     */
    private void validateLegacySignature(SignAuthContext context, ApiKeyInfo apiKeyInfo) {
        boolean isValid = SignatureUtils.verifyLegacySignature(
                apiKeyInfo.getSecretKey(),
                context.getSignature(),
                context.getRequestUri(),
                context.getTimestamp(),
                context.getNonce()
        );
        if (!isValid) {
            throw new ServiceException("兼容签名验证失败");
        }
    }

    /**
     * 验证SignedHeaders
     */
    private void validateSignedHeaders(SignAuthContext context) {
        List<String> signedHeaders = context.getSignedHeaders();
        if (signedHeaders == null || signedHeaders.isEmpty()) {
            throw new ServiceException("SignedHeaders不能为空");
        }

        // 验证必需的头部
        List<String> requiredHeaders = Arrays.asList("x-qca-timestamp", "x-qca-nonce");
        for (String requiredHeader : requiredHeaders) {
            if (!signedHeaders.contains(requiredHeader)) {
                throw new ServiceException("SignedHeaders必须包含: " + requiredHeader);
            }
        }

        // 验证头部是否存在
        Map<String, String> headers = context.getHeaders();
        for (String headerName : signedHeaders) {
            if (!headers.containsKey(headerName) && !headers.containsKey(headerName.toLowerCase())) {
                throw new ServiceException("缺少签名头部: " + headerName);
            }
        }
    }

    /**
     * 获取秘钥信息
     */
    private ApiKeyInfo getApiKeyInfo(String accessKey) {
        String keySecret = authConfig.getKeySecret();
        if (StringUtils.isBlank(keySecret)) {
            return null;
        }
        List<ApiKeyInfo> list = JSONUtil.toList(keySecret, ApiKeyInfo.class);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return list.stream().filter(t -> t.getAccessKey().equals(accessKey)).findAny().orElse(null);
    }

    /**
     * 从RPC附件中获取签名上下文
     */
    private SignAuthContext extractFromRpcAttachment() {
        RpcContext rpcContext = RpcContext.getContext();
        if (rpcContext == null) {
            return null;
        }

        // 尝试解析标准格式
        SignAuthContext standardContext = extractStandardFormat(rpcContext);
        if (standardContext != null && authConfig.supportsStandardFormat()) {
            return standardContext;
        }

        // 尝试解析兼容格式
        SignAuthContext legacyContext = extractLegacyFormat(rpcContext);
        if (legacyContext != null && authConfig.supportsLegacyFormat()) {
            return legacyContext;
        }

        return null;
    }

    /**
     * 提取标准格式认证信息
     */
    private SignAuthContext extractStandardFormat(RpcContext rpcContext) {
        String authorizationHeader = rpcContext.getAttachment("Authorization");
        if (StringUtils.isBlank(authorizationHeader)) {
            return null;
        }

        AuthorizationHeaderParser.AuthorizationInfo authInfo =
                AuthorizationHeaderParser.parseAuthorizationHeader(authorizationHeader);
        if (authInfo == null) {
            return null;
        }

        try {
            SignAuthContext context = new SignAuthContext();
            context.setAuthFormat(SignAuthContext.AuthFormat.STANDARD);
            context.setAccessKey(authInfo.getCredential());
            context.setSignature(authInfo.getSignature());
            context.setSignedHeaders(authInfo.getSignedHeaders());

            // 提取时间戳和nonce
            String timestampStr = rpcContext.getAttachment("x-qca-timestamp");
            String nonce = rpcContext.getAttachment("x-qca-nonce");

            if (StringUtils.isBlank(timestampStr) || StringUtils.isBlank(nonce)) {
                log.warn("标准格式缺少必要的时间戳或nonce头部");
                return null;
            }

            context.setTimestamp(Long.parseLong(timestampStr));
            context.setNonce(nonce);

            // 设置其他信息
            context.setRequestUri(rpcContext.getAttachment("gateway.apiName"));
            context.setGatewayApiName(rpcContext.getAttachment("gateway.apiName"));
            context.setHttpMethod(rpcContext.getAttachment("http.method"));

            // 提取所有头部信息
            Map<String, String> headers = extractAllHeaders(rpcContext);
            context.setHeaders(headers);

            // 提取请求体（如果启用）
            if (authConfig.isIncludePayload()) {
                context.setPayload(rpcContext.getAttachment("request.body"));
            }

            return context;
        } catch (NumberFormatException e) {
            log.warn("标准格式时间戳转换异常", e);
            return null;
        }
    }

    /**
     * 提取兼容格式认证信息
     */
    private SignAuthContext extractLegacyFormat(RpcContext rpcContext) {
        try {
            String accessKey = rpcContext.getAttachment("x-qca-accessKey");
            String signature = rpcContext.getAttachment("x-qca-signature");
            String timestampStr = rpcContext.getAttachment("x-qca-timestamp");
            String nonce = rpcContext.getAttachment("x-qca-nonce");

            if (StringUtils.isAnyBlank(accessKey, signature, timestampStr, nonce)) {
                return null;
            }

            SignAuthContext context = new SignAuthContext();
            context.setAuthFormat(SignAuthContext.AuthFormat.LEGACY);
            context.setAccessKey(accessKey);
            context.setSignature(signature);
            context.setTimestamp(Long.parseLong(timestampStr));
            context.setNonce(nonce);
            context.setRequestUri(rpcContext.getAttachment("gateway.apiName"));
            context.setGatewayApiName(rpcContext.getAttachment("gateway.apiName"));

            return context;
        } catch (NumberFormatException e) {
            log.warn("兼容格式时间戳转换异常", e);
            return null;
        }
    }

    /**
     * 提取所有头部信息
     */
    private Map<String, String> extractAllHeaders(RpcContext rpcContext) {
        Map<String, String> headers = new HashMap<>();

        // 添加标准头部
        addHeaderIfPresent(headers, rpcContext, "host");
        addHeaderIfPresent(headers, rpcContext, "content-type");
        addHeaderIfPresent(headers, rpcContext, "x-qca-timestamp");
        addHeaderIfPresent(headers, rpcContext, "x-qca-nonce");

        // 处理gateway.apiName
        String gatewayApiName = rpcContext.getAttachment("gateway.apiName");
        if (StringUtils.isNotBlank(gatewayApiName)) {
            switch (authConfig.getApiNameStrategy()) {
                case HOST_MAPPING:
                    headers.put("host", gatewayApiName);
                    break;
                case CUSTOM_HEADER:
                    headers.put("x-qca-api-name", gatewayApiName);
                    break;
                case URI_PATH:
                    // URI_PATH策略在URI中体现，不需要添加头部
                    break;
            }
        }

        return headers;
    }

    /**
     * 添加头部信息（如果存在）
     */
    private void addHeaderIfPresent(Map<String, String> headers, RpcContext rpcContext, String headerName) {
        String value = rpcContext.getAttachment(headerName);
        if (StringUtils.isNotBlank(value)) {
            headers.put(headerName, value);
        }
    }
}
