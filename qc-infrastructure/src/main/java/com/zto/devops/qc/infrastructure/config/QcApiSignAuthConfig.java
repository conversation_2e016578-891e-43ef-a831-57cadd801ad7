package com.zto.devops.qc.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * API签名鉴权配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "qc.api.sign.auth")
public class QcApiSignAuthConfig {
    
    /**
     * 是否启用API签名鉴权
     * 默认启用
     */
    private boolean enabled = true;
    
    /**
     * 签名默认有效期（秒）
     * 默认300秒（5分钟）
     */
    private long defaultExpireSeconds = 300;
    
    /**
     * 允许的时间偏差（秒）
     * 用于处理客户端和服务端时间不同步
     * 默认60秒
     */
    private long defaultTimeSkewSeconds = 60;
    
    /**
     * Nonce缓存过期时间（秒）
     * 用于防重放攻击，应该大于等于签名有效期
     * 默认600秒（10分钟）
     */
    private long nonceCacheExpireSeconds = 600;
    
    /**
     * 是否记录鉴权日志
     * 默认true
     */
    private boolean logEnabled = true;

    /**
     * Redis缓存前缀
     */
    private String redisCachePrefix = "qc:api:sign:auth";

    /**
     * 密钥对
     */
    private String keySecret;

    /**
     * 支持的认证格式
     * STANDARD: 仅支持标准Authorization头格式
     * LEGACY: 仅支持兼容x-qca-*头格式
     * BOTH: 同时支持两种格式
     */
    private SupportedFormat supportedFormats = SupportedFormat.BOTH;

    /**
     * 是否启用请求体签名
     */
    private boolean includePayload = true;

    /**
     * 默认签名头部列表
     */
    private String defaultSignedHeaders = "host,x-qca-timestamp,x-qca-nonce";

    /**
     * API名称处理策略
     * HOST_MAPPING: 映射到host头部
     * CUSTOM_HEADER: 使用自定义头部x-qca-api-name
     * URI_PATH: 在URI路径中体现
     */
    private ApiNameStrategy apiNameStrategy = ApiNameStrategy.CUSTOM_HEADER;

    /**
     * 支持的认证格式枚举
     */
    public enum SupportedFormat {
        STANDARD, LEGACY, BOTH
    }

    /**
     * API名称处理策略枚举
     */
    public enum ApiNameStrategy {
        HOST_MAPPING, CUSTOM_HEADER, URI_PATH
    }

    /**
     * 是否支持标准格式
     */
    public boolean supportsStandardFormat() {
        return supportedFormats == SupportedFormat.STANDARD || supportedFormats == SupportedFormat.BOTH;
    }

    /**
     * 是否支持兼容格式
     */
    public boolean supportsLegacyFormat() {
        return supportedFormats == SupportedFormat.LEGACY || supportedFormats == SupportedFormat.BOTH;
    }
}
