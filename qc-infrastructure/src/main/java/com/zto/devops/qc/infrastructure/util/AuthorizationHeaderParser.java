package com.zto.devops.qc.infrastructure.util;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Authorization头解析工具类
 * 用于解析标准HTTP Authorization头格式
 * 
 * 支持格式：
 * Authorization: HMAC-SHA256 Credential=<AccessKey>,SignedHeaders=<HeaderList>,Signature=<Signature>
 * 
 * <AUTHOR>
 */
@Slf4j
public class AuthorizationHeaderParser {

    private static final String HMAC_SHA256_PREFIX = "HMAC-SHA256";
    private static final Pattern AUTHORIZATION_PATTERN = Pattern.compile(
        "^HMAC-SHA256\\s+Credential=([^,]+),\\s*SignedHeaders=([^,]+),\\s*Signature=(.+)$"
    );

    /**
     * 解析Authorization头
     * 
     * @param authorizationHeader Authorization头的值
     * @return 解析结果，如果解析失败返回null
     */
    public static AuthorizationInfo parseAuthorizationHeader(String authorizationHeader) {
        if (StringUtils.isBlank(authorizationHeader)) {
            log.debug("Authorization header is blank");
            return null;
        }

        // 去除前后空格
        authorizationHeader = authorizationHeader.trim();

        // 检查是否以HMAC-SHA256开头
        if (!authorizationHeader.startsWith(HMAC_SHA256_PREFIX)) {
            log.debug("Authorization header does not start with HMAC-SHA256");
            return null;
        }

        // 使用正则表达式解析
        Matcher matcher = AUTHORIZATION_PATTERN.matcher(authorizationHeader);
        if (!matcher.matches()) {
            log.warn("Authorization header format is invalid: {}", authorizationHeader);
            return null;
        }

        try {
            String credential = matcher.group(1).trim();
            String signedHeadersStr = matcher.group(2).trim();
            String signature = matcher.group(3).trim();

            // 解析SignedHeaders
            List<String> signedHeaders = Arrays.asList(signedHeadersStr.split(";"));

            AuthorizationInfo info = new AuthorizationInfo();
            info.setCredential(credential);
            info.setSignedHeaders(signedHeaders);
            info.setSignature(signature);

            log.debug("Successfully parsed authorization header: credential={}, signedHeaders={}", 
                     credential, signedHeaders);
            return info;

        } catch (Exception e) {
            log.error("Error parsing authorization header: {}", authorizationHeader, e);
            return null;
        }
    }

    /**
     * 构建Authorization头
     * 
     * @param credential 访问密钥ID
     * @param signedHeaders 签名头部列表
     * @param signature 签名值
     * @return Authorization头字符串
     */
    public static String buildAuthorizationHeader(String credential, List<String> signedHeaders, String signature) {
        if (StringUtils.isBlank(credential) || signedHeaders == null || signedHeaders.isEmpty() || StringUtils.isBlank(signature)) {
            throw new IllegalArgumentException("Credential, signedHeaders and signature cannot be null or empty");
        }

        String signedHeadersStr = String.join(";", signedHeaders);
        return String.format("%s Credential=%s,SignedHeaders=%s,Signature=%s", 
                           HMAC_SHA256_PREFIX, credential, signedHeadersStr, signature);
    }

    /**
     * 验证Authorization头格式
     * 
     * @param authorizationHeader Authorization头的值
     * @return 是否为有效格式
     */
    public static boolean isValidFormat(String authorizationHeader) {
        return parseAuthorizationHeader(authorizationHeader) != null;
    }

    /**
     * Authorization信息封装类
     */
    @Data
    public static class AuthorizationInfo {
        /**
         * 访问密钥ID (Credential)
         */
        private String credential;

        /**
         * 参与签名的头部列表
         */
        private List<String> signedHeaders;

        /**
         * 签名值
         */
        private String signature;

        /**
         * 检查是否包含指定的签名头部
         * 
         * @param headerName 头部名称
         * @return 是否包含
         */
        public boolean containsSignedHeader(String headerName) {
            return signedHeaders != null && signedHeaders.contains(headerName.toLowerCase());
        }

        /**
         * 获取签名头部字符串
         * 
         * @return 以分号分隔的头部列表
         */
        public String getSignedHeadersString() {
            return signedHeaders != null ? String.join(";", signedHeaders) : "";
        }
    }
}
