package com.zto.devops.qc.infrastructure.util;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * API签名工具类
 * 实现基于HMAC-SHA256的请求签名算法
 *
 * 支持两种签名模式：
 * 1. 标准模式：HTTPMethod + CanonicalURI + CanonicalQueryString + CanonicalHeaders + SignedHeaders + HashedPayload
 * 2. 兼容模式：RequestURI + Timestamp + Nonce (保持向后兼容)
 *
 * <AUTHOR>
 */
@Slf4j
public class SignatureUtils {

    private static final String HMAC_SHA256 = "HmacSHA256";
    private static final String SHA256 = "SHA-256";

    /**
     * 生成标准API签名
     *
     * @param secretKey     访问密钥Secret
     * @param httpMethod    HTTP方法 (GET, POST, PUT, DELETE等)
     * @param canonicalUri  规范化的请求URI
     * @param queryParams   查询参数Map
     * @param headers       请求头Map
     * @param signedHeaders 参与签名的头部列表
     * @param payload       请求体内容
     * @return 签名字符串
     */
    public static String generateStandardSignature(String secretKey,
                                                 String httpMethod,
                                                 String canonicalUri,
                                                 Map<String, String> queryParams,
                                                 Map<String, String> headers,
                                                 List<String> signedHeaders,
                                                 String payload) {
        try {
            // 构造待签名字符串
            String stringToSign = buildStandardStringToSign(httpMethod, canonicalUri, queryParams, headers, signedHeaders, payload);
            log.debug("Standard StringToSign: {}", stringToSign);

            // 使用HMAC-SHA256进行签名
            return hmacSha256(secretKey, stringToSign);
        } catch (Exception e) {
            log.error("生成标准签名失败", e);
            throw new RuntimeException("生成标准签名失败", e);
        }
    }

    /**
     * 生成兼容模式API签名 (保持向后兼容)
     *
     * @param secretKey  访问密钥Secret
     * @param requestUri 请求URI
     * @param timestamp  时间戳（毫秒）
     * @param nonce      随机数
     * @return 签名字符串
     */
    public static String generateLegacySignature(String secretKey, String requestUri, long timestamp, String nonce) {
        try {
            // 构造待签名字符串 (兼容旧格式)
            String stringToSign = buildLegacyStringToSign(requestUri, timestamp, nonce);
            log.debug("Legacy StringToSign: {}", stringToSign);

            // 使用HMAC-SHA256进行签名
            return hmacSha256(secretKey, stringToSign);
        } catch (Exception e) {
            log.error("生成兼容签名失败", e);
            throw new RuntimeException("生成兼容签名失败", e);
        }
    }


    /**
     * 验证标准API签名
     *
     * @param secretKey     访问密钥Secret
     * @param signature     客户端提供的签名
     * @param httpMethod    HTTP方法
     * @param canonicalUri  规范化的请求URI
     * @param queryParams   查询参数Map
     * @param headers       请求头Map
     * @param signedHeaders 参与签名的头部列表
     * @param payload       请求体内容
     * @return 签名是否有效
     */
    public static boolean verifyStandardSignature(String secretKey,
                                                String signature,
                                                String httpMethod,
                                                String canonicalUri,
                                                Map<String, String> queryParams,
                                                Map<String, String> headers,
                                                List<String> signedHeaders,
                                                String payload) {
        try {
            String expectedSignature = generateStandardSignature(secretKey, httpMethod, canonicalUri,
                                                               queryParams, headers, signedHeaders, payload);
            return signature.equals(expectedSignature);
        } catch (Exception e) {
            log.error("验证标准签名失败", e);
            return false;
        }
    }

    /**
     * 验证兼容模式API签名
     *
     * @param secretKey  访问密钥Secret
     * @param signature  客户端提供的签名
     * @param requestUri 请求URI
     * @param timestamp  时间戳
     * @param nonce      随机数
     * @return 签名是否有效
     */
    public static boolean verifyLegacySignature(String secretKey,
                                              String signature,
                                              String requestUri,
                                              long timestamp,
                                              String nonce) {
        try {
            String expectedSignature = generateLegacySignature(secretKey, requestUri, timestamp, nonce);
            return signature.equals(expectedSignature);
        } catch (Exception e) {
            log.error("验证兼容签名失败", e);
            return false;
        }
    }

    /**
     * 构造标准待签名字符串
     * 格式：HTTPMethod + "\n" + CanonicalURI + "\n" + CanonicalQueryString + "\n" +
     *       CanonicalHeaders + "\n" + SignedHeaders + "\n" + HashedPayload
     */
    private static String buildStandardStringToSign(String httpMethod,
                                                  String canonicalUri,
                                                  Map<String, String> queryParams,
                                                  Map<String, String> headers,
                                                  List<String> signedHeaders,
                                                  String payload) {
        StringBuilder stringToSign = new StringBuilder();

        // 1. HTTP方法
        stringToSign.append(httpMethod.toUpperCase()).append("\n");

        // 2. 规范化URI
        stringToSign.append(canonicalizeUri(canonicalUri)).append("\n");

        // 3. 规范化查询字符串
        stringToSign.append(canonicalizeQueryString(queryParams)).append("\n");

        // 4. 规范化头部
        stringToSign.append(canonicalizeHeaders(headers, signedHeaders)).append("\n");

        // 5. 签名头部列表
        stringToSign.append(canonicalizeSignedHeaders(signedHeaders)).append("\n");

        // 6. 请求体哈希
        stringToSign.append(hashPayload(payload));

        return stringToSign.toString();
    }

    /**
     * 构造兼容模式待签名字符串
     * 格式：RequestURI + Timestamp + Nonce
     */
    private static String buildLegacyStringToSign(String requestUri, long timestamp, String nonce) {
        return String.format("%s%s%s", requestUri, timestamp, nonce);
    }

    /**
     * 检查时间戳是否在有效范围内
     *
     * @param timestamp       客户端时间戳
     * @param expireSeconds   过期时间（秒）
     * @param timeSkewSeconds 允许的时间偏差（秒）
     * @return 时间戳是否有效
     */
    public static boolean isTimestampValid(long timestamp, long expireSeconds, long timeSkewSeconds) {
        long currentTime = System.currentTimeMillis();
        long timeDiff = Math.abs(currentTime - timestamp);

        // 检查时间偏差
        if (timeDiff > timeSkewSeconds * 1000) {
            log.warn("时间戳偏差过大: current={}, request={}, diff={}ms", currentTime, timestamp, timeDiff);
            return false;
        }

        // 检查是否过期
        if (currentTime - timestamp > expireSeconds * 1000) {
            log.warn("签名已过期: current={}, request={}, expire={}s", currentTime, timestamp, expireSeconds);
            return false;
        }

        return true;
    }

    /**
     * HMAC-SHA256签名
     */
    private static String hmacSha256(String secretKey, String data) throws NoSuchAlgorithmException, InvalidKeyException {
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
        Mac mac = Mac.getInstance(HMAC_SHA256);
        mac.init(secretKeySpec);
        byte[] signatureBytes = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(signatureBytes);
    }

    /**
     * 规范化URI
     */
    private static String canonicalizeUri(String uri) {
        if (StringUtils.isBlank(uri)) {
            return "/";
        }
        // 确保以/开头
        if (!uri.startsWith("/")) {
            uri = "/" + uri;
        }
        return uri;
    }

    /**
     * 规范化查询字符串
     */
    private static String canonicalizeQueryString(Map<String, String> queryParams) {
        if (queryParams == null || queryParams.isEmpty()) {
            return "";
        }

        return queryParams.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .map(entry -> urlEncode(entry.getKey()) + "=" + urlEncode(entry.getValue()))
                .collect(Collectors.joining("&"));
    }

    /**
     * 规范化头部
     */
    private static String canonicalizeHeaders(Map<String, String> headers, List<String> signedHeaders) {
        if (headers == null || signedHeaders == null || signedHeaders.isEmpty()) {
            return "";
        }

        return signedHeaders.stream()
                .sorted()
                .map(headerName -> {
                    String headerValue = headers.get(headerName);
                    if (headerValue == null) {
                        headerValue = headers.get(headerName.toLowerCase());
                    }
                    if (headerValue == null) {
                        headerValue = "";
                    }
                    return headerName.toLowerCase() + ":" + headerValue.trim();
                })
                .collect(Collectors.joining("\n"));
    }

    /**
     * 规范化签名头部列表
     */
    private static String canonicalizeSignedHeaders(List<String> signedHeaders) {
        if (signedHeaders == null || signedHeaders.isEmpty()) {
            return "";
        }

        return signedHeaders.stream()
                .map(String::toLowerCase)
                .sorted()
                .collect(Collectors.joining(";"));
    }

    /**
     * 计算请求体哈希
     */
    private static String hashPayload(String payload) {
        if (payload == null) {
            payload = "";
        }

        try {
            MessageDigest digest = MessageDigest.getInstance(SHA256);
            byte[] hash = digest.digest(payload.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(hash);
        } catch (NoSuchAlgorithmException e) {
            log.error("SHA-256算法不可用", e);
            throw new RuntimeException("SHA-256算法不可用", e);
        }
    }

    /**
     * 字节数组转十六进制字符串
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * URL编码
     */
    private static String urlEncode(String value) {
        if (value == null) {
            return "";
        }
        try {
            return java.net.URLEncoder.encode(value, StandardCharsets.UTF_8.toString())
                    .replace("+", "%20")
                    .replace("*", "%2A")
                    .replace("%7E", "~");
        } catch (Exception e) {
            return value;
        }
    }

    // 保持向后兼容的方法
    /**
     * @deprecated 使用 generateLegacySignature 替代
     */
    @Deprecated
    public static String generateSignature(String secretKey, String requestUri, long timestamp, String nonce) {
        return generateLegacySignature(secretKey, requestUri, timestamp, nonce);
    }

    /**
     * @deprecated 使用 verifyLegacySignature 替代
     */
    @Deprecated
    public static boolean verifySignature(String secretKey, String signature, String requestUri, long timestamp, String nonce) {
        return verifyLegacySignature(secretKey, signature, requestUri, timestamp, nonce);
    }
}
