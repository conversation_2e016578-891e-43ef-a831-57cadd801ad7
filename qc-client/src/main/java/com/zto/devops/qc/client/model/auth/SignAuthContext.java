package com.zto.devops.qc.client.model.auth;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 签名鉴权上下文
 * 用于在Dubbo调用过程中传递签名参数
 *
 * 支持两种认证格式：
 * 1. 标准格式：Authorization头 + x-qca-timestamp + x-qca-nonce
 * 2. 兼容格式：x-qca-accessKey + x-qca-signature + x-qca-timestamp + x-qca-nonce
 */
@Data
public class SignAuthContext {
    /**
     * 访问密钥ID
     */
    private String accessKey;

    /**
     * 签名
     */
    private String signature;

    /**
     * 时间戳
     */
    private long timestamp;

    /**
     * 随机数
     */
    private String nonce;

    /**
     * 请求URI
     */
    private String requestUri;

    /**
     * HTTP方法
     */
    private String httpMethod;

    /**
     * 查询参数
     */
    private Map<String, String> queryParams;

    /**
     * 请求头
     */
    private Map<String, String> headers;

    /**
     * 参与签名的头部列表
     */
    private List<String> signedHeaders;

    /**
     * 请求体内容
     */
    private String payload;

    /**
     * 认证格式类型
     */
    private AuthFormat authFormat;

    /**
     * 网关API名称 (用于替代host)
     */
    private String gatewayApiName;

    /**
     * 认证格式枚举
     */
    public enum AuthFormat {
        /**
         * 标准格式：Authorization头
         */
        STANDARD,

        /**
         * 兼容格式：x-qca-*头
         */
        LEGACY
    }

    /**
     * 是否为标准格式
     */
    public boolean isStandardFormat() {
        return AuthFormat.STANDARD.equals(authFormat);
    }

    /**
     * 是否为兼容格式
     */
    public boolean isLegacyFormat() {
        return AuthFormat.LEGACY.equals(authFormat);
    }
}
