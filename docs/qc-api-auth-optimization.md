# QC API签名认证系统优化方案

## 当前问题分析

### 1. 非标准HTTP头格式
- 当前使用：`x-qca-accessKey`, `x-qca-signature`, `x-qca-nonce`, `x-qca-timestamp`
- 问题：不符合标准HTTP认证头格式

### 2. Authorization头未实际使用
- 当前代码中提到HMAC-SHA256和SignedHeaders，但实际未实现
- 签名算法简化，未充分利用标准认证机制

### 3. 签名计算不完整
- 当前签名：`requestUri + timestamp + nonce`
- 缺少：HTTP方法、请求体、标准头部信息

### 4. 使用gateway.apiName而非host
- 当前使用`gateway.apiName`作为请求标识
- 标准做法应该使用host或其他标准头部

## 优化方案设计

### 1. 新的HTTP头格式

#### Authorization头格式
```
Authorization: HMAC-SHA256 Credential=<AccessKey>,SignedHeaders=<SignedHeadersList>,Signature=<CalculatedSignature>
```

#### 保留的自定义头
```
x-qca-timestamp: 20231101T120000Z
x-qca-nonce: 550e8400-e29b-41d4-a716-446655440000
```

#### 示例
```
Authorization: HMAC-SHA256 Credential=AKIAIOSFODNN7EXAMPLE,SignedHeaders=host;x-qca-timestamp;x-qca-nonce,Signature=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
x-qca-timestamp: 20231101T120000Z
x-qca-nonce: 550e8400-e29b-41d4-a716-446655440000
```

### 2. 优化的签名算法

#### 签名字符串构造
```
StringToSign = HTTPMethod + "\n" +
               CanonicalURI + "\n" +
               CanonicalQueryString + "\n" +
               CanonicalHeaders + "\n" +
               SignedHeaders + "\n" +
               HashedPayload
```

#### 各部分说明
- **HTTPMethod**: GET, POST, PUT, DELETE等
- **CanonicalURI**: 规范化的请求路径
- **CanonicalQueryString**: 规范化的查询参数
- **CanonicalHeaders**: 规范化的签名头部
- **SignedHeaders**: 参与签名的头部名称列表
- **HashedPayload**: 请求体的SHA256哈希值

#### SignedHeaders处理
- 支持的头部：`host`, `x-qca-timestamp`, `x-qca-nonce`, `content-type`
- 对于gateway.apiName，可以通过以下方式处理：
  1. 映射到host头部
  2. 使用自定义头部`x-qca-api-name`
  3. 在CanonicalURI中体现

### 3. 向后兼容性

#### 双格式支持
系统将同时支持：
1. 新格式：Authorization头 + x-qca-timestamp + x-qca-nonce
2. 旧格式：x-qca-accessKey + x-qca-signature + x-qca-timestamp + x-qca-nonce

#### 迁移策略
1. 新系统优先使用新格式
2. 检测到旧格式时自动兼容
3. 提供配置开关控制格式支持

## 实现计划

### 阶段1：核心组件重构
1. 重构`SignatureUtils`类
2. 创建`AuthorizationHeaderParser`工具类
3. 更新`SignAuthContext`模型

### 阶段2：认证逻辑更新
1. 更新`QcApiSignAuthAspect`
2. 实现双格式支持
3. 添加配置管理

### 阶段3：测试和验证
1. 编写单元测试
2. 集成测试
3. 性能测试

## 配置参数

### 新增配置项
```yaml
qc:
  api:
    sign:
      auth:
        # 支持的认证格式：STANDARD, LEGACY, BOTH
        supported-formats: BOTH
        # 是否启用请求体签名
        include-payload: true
        # 默认签名头部
        default-signed-headers: host,x-qca-timestamp,x-qca-nonce
        # API名称处理方式：HOST_MAPPING, CUSTOM_HEADER, URI_PATH
        api-name-strategy: CUSTOM_HEADER
```

## 安全性增强

### 1. 更强的签名算法
- 包含更多请求信息
- 防止请求篡改
- 支持请求体完整性验证

### 2. 标准化合规
- 符合HTTP认证标准
- 便于第三方集成
- 提高系统互操作性

### 3. 灵活的头部管理
- 可配置的签名头部
- 支持自定义头部
- 适应不同场景需求
